import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { useToast } from "@/hooks/use-toast";
import { apiRequest, queryClient } from "@/lib/queryClient";
import { User } from "@shared/schema";
import { Play, Minus, Plus, Zap, RotateCcw } from "lucide-react";
import { AudioManager } from "./audio-manager";
import { cascadeManager } from "./cascade-manager";
import { SymbolDisplay } from "./symbol-display";
import { useIsMobile } from "@/hooks/use-mobile";
import "./reel-animation-styles.css";

interface SlotMachineProps {
  user: User;
}

interface GameSession {
  sessionId: string;
  currentBalance: string;
  freeSpinsRemaining: number;
  currentMultiplier: number;
}

interface SpinResult {
  grid: string[][];
  wins: any[];
  totalWin: string;
  newBalance: string;
  multiplier: number;
  scatterTriggered: boolean;
  bonusTriggered: boolean;
  freeSpinsRemaining: number;
  spinId: number;
  winAmount: number;
  winLines: any[];
  scatterCount: number;
  hasBonus: boolean;
  freeSpinsTriggered: number;
}

const betAmounts = [0.10, 0.25, 0.50, 1.00, 2.50, 5.00, 10.00, 25.00, 50.00, 100.00];

export function SlotMachine({ user }: SlotMachineProps) {
  const { toast } = useToast();
  const isMobile = useIsMobile();
  const [gameSession, setGameSession] = useState<GameSession | null>(null);
  const [currentBet, setCurrentBet] = useState(2.50);
  const [currentBetIndex, setCurrentBetIndex] = useState(4);
  const [isSpinning, setIsSpinning] = useState(false);
  const [reelGrid, setReelGrid] = useState<string[][]>([]);
  const [lastWin, setLastWin] = useState("0.00");
  const [autoSpin, setAutoSpin] = useState(false);
  const [autoSpinCount, setAutoSpinCount] = useState(0);
  const [showWinCelebration, setShowWinCelebration] = useState(false);
  const [winAmount, setWinAmount] = useState("0.00");
  const [showBonusModal, setShowBonusModal] = useState(false);
  const [consecutiveLosses, setConsecutiveLosses] = useState(0);

  // Initialize audio manager
  const audioManager = new AudioManager();

  // Initialize game session
  useEffect(() => {
    if (!gameSession) {
      initializeSession();
    }
  }, [gameSession]);

  // Auto-spin logic
  useEffect(() => {
    if (autoSpin && autoSpinCount > 0 && !isSpinning && gameSession) {
      const balance = parseFloat(gameSession.currentBalance);
      if (balance >= currentBet) {
        const timer = setTimeout(() => {
          performSpin();
          setAutoSpinCount(prev => prev - 1);
        }, 2000);
        return () => clearTimeout(timer);
      } else {
        setAutoSpin(false);
        setAutoSpinCount(0);
        toast({
          title: "Auto-spin stopped",
          description: "Insufficient balance to continue",
          variant: "destructive",
        });
      }
    }
  }, [autoSpin, autoSpinCount, isSpinning, gameSession, currentBet]);

  const initializeSession = async () => {
    console.log("🎮 Initializing game session...");
    try {
      console.log("🎮 Making request to /api/game/start-session");
      const response = await apiRequest("POST", "/api/game/start-session", {});
      console.log("🎮 Session response:", response);
      const session = await response.json();
      console.log("🎮 Session data:", session);
      setGameSession(session);

      // Initialize with random symbols
      const initialGrid: string[][] = [];
      for (let reel = 0; reel < 5; reel++) {
        initialGrid[reel] = [];
        for (let row = 0; row < 4; row++) {
          const symbols = ['9', '10', 'J', 'Q', 'K', 'A', 'SCATTER', 'WILD'];
          initialGrid[reel][row] = symbols[Math.floor(Math.random() * symbols.length)];
        }
      }
      setReelGrid(initialGrid);
      console.log("🎮 Game session initialized successfully!");
    } catch (error) {
      console.error("🎮 Session initialization error:", error);
      toast({
        title: "Session Error",
        description: "Failed to initialize game session",
        variant: "destructive",
      });
    }
  };

  const adjustBet = (direction: 'increase' | 'decrease') => {
    if (direction === 'increase' && currentBetIndex < betAmounts.length - 1) {
      const newIndex = currentBetIndex + 1;
      setCurrentBetIndex(newIndex);
      setCurrentBet(betAmounts[newIndex]);
    } else if (direction === 'decrease' && currentBetIndex > 0) {
      const newIndex = currentBetIndex - 1;
      setCurrentBetIndex(newIndex);
      setCurrentBet(betAmounts[newIndex]);
    }
  };

  const setMaxBet = () => {
    const maxIndex = betAmounts.length - 1;
    setCurrentBetIndex(maxIndex);
    setCurrentBet(betAmounts[maxIndex]);
  };

  const performSpin = async () => {
    console.log("🎰 Spin button clicked!");
    console.log("🎰 Game session:", gameSession);
    console.log("🎰 Is spinning:", isSpinning);

    if (!gameSession || isSpinning) {
      console.log("🎰 Spin blocked - no session or already spinning");
      return;
    }

    const balance = parseFloat(gameSession.currentBalance);
    const isFreeSpinRound = gameSession.freeSpinsRemaining > 0;

    console.log("🎰 Balance:", balance, "Current bet:", currentBet);
    console.log("🎰 Free spin round:", isFreeSpinRound);

    if (!isFreeSpinRound && balance < currentBet) {
      console.log("🎰 Insufficient balance for spin");
      toast({
        title: "Insufficient Balance",
        description: "You don't have enough credits to spin",
        variant: "destructive",
      });
      return;
    }

    console.log("🎰 Starting spin...");
    setIsSpinning(true);
    audioManager.playSpinSound();

    try {
      console.log("🎰 Making API request to /api/game/spin");
      // Call backend spin API FIRST to get final result
      const response = await apiRequest("POST", "/api/game/spin", {
        sessionId: gameSession.sessionId,
        betAmount: currentBet.toFixed(2),
        isFreeSpinRound,
      });

      console.log("🎰 API response received:", response);
      const result: SpinResult = await response.json();
      console.log("🎰 Spin result:", result);

      // Animate reels with final result (simple animation)
      await animateReelsWithResult(result.grid);

      // Handle cascading if enabled
      let finalWinAmount = parseFloat(result.totalWin);
      let finalGrid = result.grid;

      if (result.cascadeCount > 0 && result.wins && result.wins.length > 0) {
        console.log(`🌊 Processing ${result.cascadeCount} cascades`);

        // Process cascades
        const cascadeResult = await cascadeManager.processCascades(
          result.grid,
          result.wins,
          result.cascadeCount
        );

        // Update grid and win amount with cascade results
        finalGrid = cascadeResult.newGrid;
        finalWinAmount += cascadeResult.totalWinAmount;

        console.log(`🌊 Cascades completed! Total cascades: ${cascadeResult.totalCascades}, Extra win: ${cascadeResult.totalWinAmount}`);

        // Show cascade completion message
        if (cascadeResult.totalCascades > 0) {
          toast({
            title: `🌊 ${cascadeResult.totalCascades} Cascades!`,
            description: `Extra wins: ${cascadeResult.totalWinAmount.toFixed(2)} coins!`,
            duration: 4000,
          });
        }
      }

      // Set final grid
      setReelGrid(finalGrid);

      // Update game state immediately after reels stop
      console.log(`💰 Frontend: Updating balance from ${gameSession?.currentBalance} to ${result.newBalance}`);
      setGameSession(prev => prev ? {
        ...prev,
        currentBalance: result.newBalance,
        freeSpinsRemaining: result.freeSpinsRemaining,
        currentMultiplier: result.multiplier,
      } : null);

      setLastWin(finalWinAmount.toFixed(2));

      // Handle wins and update consecutive losses
      if (finalWinAmount > 0) {
        // Reset consecutive losses on win
        setConsecutiveLosses(0);

        audioManager.playWinSound(finalWinAmount, currentBet);
        setWinAmount(finalWinAmount.toFixed(2));

        // Show celebration for big wins
        if (finalWinAmount >= currentBet * 10) {
          setShowWinCelebration(true);
          setTimeout(() => setShowWinCelebration(false), 3000);
        }

        // Highlight winning symbols
        highlightWinningSymbols(result.wins);
      } else {
        // Increment consecutive losses on loss
        setConsecutiveLosses(prev => prev + 1);
        setWinAmount("0.00");
      }

      // Handle bonus triggers
      if (result.bonusTriggered) {
        setShowBonusModal(true);
        setTimeout(() => setShowBonusModal(false), 3000);
      }

      // Update user data in cache
      queryClient.setQueryData(["/api/user"], {
        ...user,
        balance: result.newBalance,
      });

    } catch (error) {
      console.error("🎰 Spin error:", error);

      // Check if it's an authentication error
      if (error instanceof Error && error.message.includes("401")) {
        toast({
          title: "Authentication Required",
          description: "Please log in again to continue playing",
          variant: "destructive",
        });
        // Redirect to login
        window.location.href = '/auth';
      } else {
        toast({
          title: "Spin Failed",
          description: `An error occurred while spinning: ${error instanceof Error ? error.message : 'Unknown error'}`,
          variant: "destructive",
        });
      }
    } finally {
      console.log("🎰 Spin completed, setting isSpinning to false");
      setIsSpinning(false);
    }
  };

  // 🎰 Generate varied drop patterns to prevent predictability
  const generateVariedDropPattern = () => {
    const patterns = [
      // Sequential patterns (traditional)
      { name: "Classic Left-Right", timings: [800, 1000, 1200, 1400, 1600], speeds: [1.0, 1.0, 1.0, 1.0, 1.0], effects: ['none', 'none', 'none', 'none', 'none'] },
      { name: "Fast Sequential", timings: [600, 750, 900, 1050, 1200], speeds: [1.2, 1.2, 1.2, 1.2, 1.2], effects: ['none', 'none', 'none', 'none', 'none'] },
      { name: "Slow Build", timings: [1000, 1300, 1600, 1900, 2200], speeds: [0.8, 0.8, 0.8, 0.8, 0.8], effects: ['none', 'none', 'none', 'none', 'none'] },

      // Random patterns
      { name: "Random Chaos", timings: [800, 1200, 900, 1500, 1100], speeds: [1.1, 0.9, 1.3, 0.7, 1.0], effects: ['none', 'glow', 'none', 'pulse', 'none'] },
      { name: "Scattered Drop", timings: [1100, 800, 1400, 950, 1250], speeds: [0.9, 1.2, 0.8, 1.1, 0.95], effects: ['pulse', 'none', 'none', 'none', 'glow'] },
      { name: "Wild Timing", timings: [700, 1350, 850, 1150, 1450], speeds: [1.3, 0.7, 1.2, 0.9, 0.8], effects: ['none', 'none', 'bounce', 'none', 'none'] },

      // Center-out patterns
      { name: "Center Explosion", timings: [1200, 1000, 800, 1000, 1200], speeds: [0.8, 1.0, 1.2, 1.0, 0.8], effects: ['none', 'none', 'glow', 'none', 'none'] },
      { name: "Center Focus", timings: [1400, 1100, 900, 1100, 1400], speeds: [0.7, 0.9, 1.3, 0.9, 0.7], effects: ['pulse', 'none', 'intense', 'none', 'pulse'] },

      // Outside-in patterns
      { name: "Edge Squeeze", timings: [800, 1200, 1600, 1200, 800], speeds: [1.2, 0.9, 0.6, 0.9, 1.2], effects: ['glow', 'none', 'none', 'none', 'glow'] },
      { name: "Pincer Movement", timings: [900, 1300, 1500, 1300, 900], speeds: [1.1, 0.8, 0.7, 0.8, 1.1], effects: ['none', 'pulse', 'none', 'pulse', 'none'] },

      // Wave patterns
      { name: "Wave Right", timings: [800, 900, 1000, 1100, 1200], speeds: [1.2, 1.1, 1.0, 0.9, 0.8], effects: ['none', 'none', 'none', 'none', 'none'] },
      { name: "Wave Left", timings: [1200, 1100, 1000, 900, 800], speeds: [0.8, 0.9, 1.0, 1.1, 1.2], effects: ['none', 'none', 'none', 'none', 'none'] },
      { name: "Double Wave", timings: [1000, 800, 1200, 800, 1000], speeds: [1.0, 1.3, 0.8, 1.3, 1.0], effects: ['none', 'glow', 'none', 'glow', 'none'] },

      // Bounce patterns
      { name: "Bounce Back", timings: [800, 1000, 1200, 1000, 800], speeds: [1.2, 1.0, 0.8, 1.0, 1.2], effects: ['bounce', 'none', 'none', 'none', 'bounce'] },
      { name: "Rubber Band", timings: [700, 1100, 1500, 1100, 700], speeds: [1.4, 0.9, 0.6, 0.9, 1.4], effects: ['none', 'none', 'bounce', 'none', 'none'] },

      // Dramatic patterns
      { name: "Suspense Build", timings: [1000, 1200, 1400, 1800, 2200], speeds: [1.0, 0.9, 0.8, 0.6, 0.4], effects: ['none', 'none', 'pulse', 'glow', 'intense'] },
      { name: "Quick Finish", timings: [1200, 1400, 1600, 1000, 800], speeds: [0.8, 0.7, 0.6, 1.2, 1.4], effects: ['none', 'none', 'none', 'glow', 'intense'] },

      // Zigzag patterns
      { name: "Zigzag Right", timings: [800, 1200, 900, 1300, 1000], speeds: [1.2, 0.8, 1.1, 0.7, 1.0], effects: ['none', 'none', 'zigzag', 'none', 'none'] },
      { name: "Zigzag Left", timings: [1300, 900, 1200, 800, 1000], speeds: [0.7, 1.1, 0.8, 1.2, 1.0], effects: ['none', 'zigzag', 'none', 'none', 'none'] },

      // Special patterns
      { name: "Heartbeat", timings: [1000, 800, 1000, 800, 1000], speeds: [1.0, 1.3, 1.0, 1.3, 1.0], effects: ['pulse', 'none', 'pulse', 'none', 'pulse'] },
      { name: "Spiral In", timings: [1200, 1000, 800, 1000, 1200], speeds: [0.8, 1.0, 1.2, 1.0, 0.8], effects: ['spiral', 'none', 'none', 'none', 'spiral'] },
      { name: "Cascade Fall", timings: [800, 850, 900, 950, 1000], speeds: [1.2, 1.15, 1.1, 1.05, 1.0], effects: ['cascade', 'cascade', 'cascade', 'cascade', 'cascade'] },

      // Advanced patterns for maximum variety
      { name: "Stutter Step", timings: [800, 850, 900, 850, 900], speeds: [1.2, 1.1, 1.0, 1.1, 1.0], effects: ['none', 'shake', 'none', 'shake', 'none'] },
      { name: "Rubber Snap", timings: [1200, 1400, 800, 600, 400], speeds: [0.8, 0.7, 1.3, 1.5, 1.8], effects: ['rubber', 'none', 'none', 'none', 'intense'] },
      { name: "Flip Flop", timings: [900, 1100, 900, 1100, 900], speeds: [1.1, 0.9, 1.1, 0.9, 1.1], effects: ['flip', 'none', 'flip', 'none', 'flip'] },
      { name: "Wobble Dance", timings: [1000, 1200, 1000, 1200, 1000], speeds: [1.0, 0.8, 1.0, 0.8, 1.0], effects: ['wobble', 'none', 'wobble', 'none', 'wobble'] },
      { name: "Thunder Roll", timings: [1500, 1200, 900, 600, 300], speeds: [0.6, 0.8, 1.1, 1.4, 1.7], effects: ['none', 'shake', 'pulse', 'glow', 'intense'] },
      { name: "Gentle Breeze", timings: [1100, 1150, 1200, 1250, 1300], speeds: [0.9, 0.85, 0.8, 0.75, 0.7], effects: ['none', 'none', 'none', 'none', 'none'] },
      { name: "Chaos Theory", timings: [750, 1350, 950, 1150, 850], speeds: [1.3, 0.7, 1.1, 0.9, 1.2], effects: ['shake', 'spiral', 'bounce', 'flip', 'wobble'] },
      { name: "Mirror Match", timings: [1000, 1200, 1400, 1200, 1000], speeds: [1.0, 0.8, 0.6, 0.8, 1.0], effects: ['glow', 'pulse', 'intense', 'pulse', 'glow'] },
      { name: "Pendulum Swing", timings: [800, 1000, 1200, 1000, 800], speeds: [1.2, 1.0, 0.8, 1.0, 1.2], effects: ['none', 'none', 'none', 'none', 'none'] },
      { name: "Earthquake", timings: [900, 950, 1000, 950, 900], speeds: [1.1, 1.05, 1.0, 1.05, 1.1], effects: ['shake', 'shake', 'shake', 'shake', 'shake'] },
    ];

    // 🎯 Smart pattern selection based on game state
    let candidatePatterns = [...patterns];

    // Filter patterns based on consecutive losses for psychological effect
    if (consecutiveLosses >= 5) {
      // Use more dramatic patterns for players on losing streaks
      candidatePatterns = patterns.filter(p =>
        p.name.includes('Thunder') || p.name.includes('Intense') ||
        p.name.includes('Dramatic') || p.name.includes('Suspense')
      );
    } else if (consecutiveLosses >= 3) {
      // Use anticipation-building patterns
      candidatePatterns = patterns.filter(p =>
        p.name.includes('Build') || p.name.includes('Focus') ||
        p.name.includes('Anticipation') || p.effects.includes('pulse')
      );
    }

    // Ensure we have candidates
    if (candidatePatterns.length === 0) {
      candidatePatterns = patterns;
    }

    // Add multiple layers of randomization
    const timeVariation = Date.now() % 1000;
    const userVariation = (user?.id || 1) % 100;
    const sessionVariation = gameSession?.sessionId.length || 10;
    const betVariation = Math.floor(currentBet * 100) % 50;

    // Combine all variations for maximum unpredictability
    const combinedSeed = timeVariation + userVariation + sessionVariation + betVariation;
    const patternIndex = (combinedSeed + Math.floor(Math.random() * 10000)) % candidatePatterns.length;
    const selectedPattern = candidatePatterns[patternIndex];

    // Add micro-variations to timings (±100ms random)
    const variedTimings = selectedPattern.timings.map(timing => {
      const baseVariation = (Math.random() - 0.5) * 200;
      const userVariation = ((user?.id || 1) % 10 - 5) * 10;
      return Math.max(300, timing + baseVariation + userVariation);
    });

    // Add micro-variations to speeds (±0.2 random)
    const variedSpeeds = selectedPattern.speeds.map(speed => {
      const baseVariation = (Math.random() - 0.5) * 0.4;
      const betVariation = (currentBet - 2.5) * 0.02; // Slight speed change based on bet
      return Math.max(0.4, Math.min(2.0, speed + baseVariation + betVariation));
    });

    // Occasionally add random effect swaps for extra unpredictability
    const variedEffects = [...selectedPattern.effects];
    if (Math.random() < 0.3) { // 30% chance to modify effects
      const effectOptions = ['none', 'glow', 'pulse', 'bounce', 'shake', 'wobble'];
      const randomIndex = Math.floor(Math.random() * 5);
      variedEffects[randomIndex] = effectOptions[Math.floor(Math.random() * effectOptions.length)];
    }

    return {
      ...selectedPattern,
      timings: variedTimings,
      speeds: variedSpeeds,
      effects: variedEffects,
      name: `${selectedPattern.name} (${combinedSeed % 1000})`
    };
  };

  const animateReelsWithResult = async (finalGrid: string[][]): Promise<void> => {
    return new Promise((resolve) => {
      // Check if we're on mobile or desktop
      const isMobileLayout = isMobile;

      if (isMobileLayout) {
        // Mobile: animate individual symbol cells
        const symbolCells = document.querySelectorAll('.mobile-symbol-cell');
        let completedCells = 0;
        const totalCells = Math.min(9, symbolCells.length); // 3x3 grid

        // 🎰 Generate unique drop pattern for this spin
        const dropPattern = generateVariedDropPattern();
        console.log(`🎰 Mobile: Using drop pattern: ${dropPattern.name}`);

        symbolCells.forEach((cell, index) => {
          if (index < totalCells) { // Only animate first 9 cells (3x3)
            console.log(`🎰 Mobile: Animating cell ${index} with class reel-spinning`);
            cell.classList.add('reel-spinning');

            // Apply pattern-specific visual effects
            const effectIndex = index % dropPattern.effects.length;
            if (dropPattern.effects[effectIndex] !== 'none') {
              console.log(`🎰 Mobile: Adding effect reel-${dropPattern.effects[effectIndex]} to cell ${index}`);
              cell.classList.add(`reel-${dropPattern.effects[effectIndex]}`);
            }

            const animationDuration = dropPattern.timings[effectIndex] * dropPattern.speeds[effectIndex];
            console.log(`🎰 Mobile: Cell ${index} will animate for ${animationDuration}ms`);

            setTimeout(() => {
              console.log(`🎰 Mobile: Stopping animation for cell ${index}`);
              cell.classList.remove('reel-spinning');

              // Remove effect classes
              dropPattern.effects.forEach(effect => {
                if (effect !== 'none') {
                  cell.classList.remove(`reel-${effect}`);
                }
              });

              completedCells++;

              if (completedCells === totalCells) {
                console.log(`🎰 Mobile: All ${totalCells} cells completed animation`);
                // Set final grid immediately when all cells stop
                setReelGrid(finalGrid);
                resolve();
              }
            }, animationDuration);
          }
        });

        // Fallback in case no cells are found
        if (totalCells === 0) {
          setReelGrid(finalGrid);
          resolve();
        }
      } else {
        // Desktop: animate reel columns
        const reels = document.querySelectorAll('.reel-column');
        let completedReels = 0;

        // 🎰 Generate unique drop pattern for this spin
        const dropPattern = generateVariedDropPattern();
        console.log(`🎰 Desktop: Using drop pattern: ${dropPattern.name}`);

        reels.forEach((reel, index) => {
          reel.classList.add('reel-spinning');

          // Apply pattern-specific visual effects
          if (dropPattern.effects[index] !== 'none') {
            reel.classList.add(`reel-${dropPattern.effects[index]}`);
          }

          setTimeout(() => {
            reel.classList.remove('reel-spinning');

            // Remove effect classes
            dropPattern.effects.forEach(effect => {
              if (effect !== 'none') {
                reel.classList.remove(`reel-${effect}`);
              }
            });

            completedReels++;

            if (completedReels === reels.length) {
              // Set final grid immediately when all reels stop
              setReelGrid(finalGrid);
              resolve();
            }
          }, dropPattern.timings[index] * dropPattern.speeds[index]);
        });

        // Fallback in case no reels are found
        if (reels.length === 0) {
          setReelGrid(finalGrid);
          resolve();
        }
      }
    });
  };


  const highlightWinningSymbols = (wins: any[]) => {
    // Clear any existing highlights
    document.querySelectorAll('.winning-symbol').forEach(el => {
      el.classList.remove('winning-symbol');
    });

    // Add highlights for winning positions
    wins.forEach(win => {
      if (win.positions && Array.isArray(win.positions)) {
        win.positions.forEach((pos: { reel: number; row: number }) => {
          const symbolElement = document.querySelector(
            `[data-reel="${pos.reel}"][data-row="${pos.row}"]`
          );
          if (symbolElement) {
            symbolElement.classList.add('winning-symbol');
            // Remove highlight after animation
            setTimeout(() => {
              symbolElement.classList.remove('winning-symbol');
            }, 3000);
          }
        });
      }
    });
  };

  const startAutoSpin = (count: number) => {
    setAutoSpinCount(count);
    setAutoSpin(true);
  };

  const stopAutoSpin = () => {
    setAutoSpin(false);
    setAutoSpinCount(0);
  };

  // Removed getSymbolDisplay - now using SymbolDisplay component

  if (!gameSession) {
    return (
      <div className="flex items-center justify-center h-96">
        <div className="text-center">
          <div className="w-16 h-16 border-4 border-casino-gold border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-casino-gold font-orbitron">Initializing game...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Simple CSS animations with cascading */}
      <style>{`
        .reel-spinning {
          animation: spin 0.1s linear infinite;
        }

        .winning-symbol {
          animation: winPulse 1s ease-in-out 3;
          border-color: #ffd700 !important;
          background-color: rgba(255, 215, 0, 0.2) !important;
          box-shadow: 0 0 20px rgba(255, 215, 0, 0.6);
        }

        .cascade-remove {
          animation: cascadeRemove 0.8s ease-in-out forwards;
        }

        .cascade-drop {
          animation: cascadeDrop 0.6s ease-in-out;
        }

        @keyframes spin {
          0% { transform: translateY(0); }
          100% { transform: translateY(-10px); }
        }

        @keyframes winPulse {
          0%, 100% { transform: scale(1); }
          50% { transform: scale(1.1); }
        }

        @keyframes cascadeRemove {
          0% {
            transform: scale(1);
            opacity: 1;
          }
          50% {
            transform: scale(1.2);
            opacity: 0.8;
          }
          100% {
            transform: scale(0);
            opacity: 0;
          }
        }

        @keyframes cascadeDrop {
          0% {
            transform: translateY(-20px);
            opacity: 0.7;
          }
          50% {
            transform: translateY(5px);
            opacity: 0.9;
          }
          100% {
            transform: translateY(0);
            opacity: 1;
          }
        }
      `}</style>

      {/* Multiplier and Free Spins Displays */}
      <div className="flex justify-between items-start">
        {gameSession.currentMultiplier > 1 && (
          <Card className="glass-card border-casino-gold/50">
            <CardContent className="p-4 text-center">
              <p className="text-xs text-gray-400 uppercase">Multiplier</p>
              <p className="text-2xl font-bold text-casino-gold animate-pulse">
                x{gameSession.currentMultiplier}
              </p>
            </CardContent>
          </Card>
        )}

        {gameSession.freeSpinsRemaining > 0 && (
          <Card className="glass-card border-casino-purple/50">
            <CardContent className="p-4 text-center">
              <p className="text-xs text-gray-400 uppercase">Free Spins</p>
              <p className="text-2xl font-bold text-casino-purple animate-pulse">
                {gameSession.freeSpinsRemaining}
              </p>
            </CardContent>
          </Card>
        )}
      </div>

      {/* Slot Machine Grid */}
      <Card className={`glass-card border-casino-gold/30 ${isMobile ? 'mobile-slot-container' : 'p-8'}`}>
        {isMobile ? (
          // Mobile 3x3 Grid
          <div className="mobile-slot-grid">
            {reelGrid.slice(0, 3).map((reel, reelIndex) =>
              reel.slice(0, 3).map((symbol, rowIndex) => (
                <div
                  key={`${reelIndex}-${rowIndex}`}
                  data-reel={reelIndex}
                  data-row={rowIndex}
                  className="mobile-symbol-cell"
                >
                  <SymbolDisplay symbol={symbol} size="medium" />
                </div>
              ))
            )}
          </div>
        ) : (
          // Desktop 5x4 Grid
          <div className="grid grid-cols-5 gap-3 mb-6">
            {reelGrid.map((reel, reelIndex) => (
              <div key={reelIndex} className="reel-column space-y-2">
                {reel.map((symbol, rowIndex) => (
                  <div
                    key={`${reelIndex}-${rowIndex}`}
                    data-reel={reelIndex}
                    data-row={rowIndex}
                    className="symbol-container glass-card border border-casino-gold/20 rounded-xl h-20 flex items-center justify-center transition-all duration-300 hover:border-casino-gold/50"
                  >
                    <SymbolDisplay symbol={symbol} size="medium" />
                  </div>
                ))}
              </div>
            ))}
          </div>
        )}

        {/* Game Info */}
        <div className="text-center space-y-2">
          {!isMobile && <p className="text-sm text-gray-400">1024 Ways to Win</p>}
          <div className="h-8 flex items-center justify-center">
            {parseFloat(lastWin) > 0 ? (
              <div className={`font-bold text-casino-gold animate-pulse ${isMobile ? 'text-lg' : 'text-2xl'}`}>
                Last Win: ${lastWin}
              </div>
            ) : (
              <div className={`font-bold text-transparent ${isMobile ? 'text-lg' : 'text-2xl'}`}>
                Last Win: $0.00
              </div>
            )}
          </div>
        </div>
      </Card>

      {/* Controls */}
      <Card className={`glass-card border-casino-gold/30 ${isMobile ? 'mobile-controls' : 'p-6'}`}>
        {isMobile ? (
          // Mobile Controls Layout
          <div className="space-y-4">
            {/* Bet Controls */}
            <div className="mobile-bet-controls">
              <button
                onClick={() => adjustBet('decrease')}
                disabled={currentBetIndex === 0 || isSpinning}
                className="mobile-bet-button"
              >
                -
              </button>

              <div className="mobile-bet-display">
                <p className="text-xs text-gray-400 uppercase">BET</p>
                <p className="text-lg font-bold text-casino-gold">
                  ${currentBet.toFixed(2)}
                </p>
              </div>

              <button
                onClick={() => adjustBet('increase')}
                disabled={currentBetIndex === betAmounts.length - 1 || isSpinning}
                className="mobile-bet-button"
              >
                +
              </button>
            </div>

            {/* Spin Button */}
            <button
              onClick={performSpin}
              disabled={isSpinning || (!gameSession.freeSpinsRemaining && parseFloat(gameSession.currentBalance) < currentBet)}
              className="mobile-spin-button"
            >
              {isSpinning ? "SPINNING..." :
               gameSession.freeSpinsRemaining > 0 ? "FREE SPIN" : "SPIN"}
            </button>

            {/* Action Buttons */}
            <div className="mobile-action-buttons">
              <button
                onClick={setMaxBet}
                disabled={isSpinning}
                className="mobile-action-button"
              >
                MAX BET
              </button>
              {!autoSpin ? (
                <button
                  onClick={() => startAutoSpin(25)}
                  disabled={isSpinning}
                  className="mobile-action-button"
                >
                  AUTO 25
                </button>
              ) : (
                <button
                  onClick={stopAutoSpin}
                  className="mobile-action-button active"
                >
                  STOP ({autoSpinCount})
                </button>
              )}
            </div>
          </div>
        ) : (
          // Desktop Controls Layout
          <div className="flex flex-col lg:flex-row justify-between items-center space-y-4 lg:space-y-0 lg:space-x-6">
            {/* Bet Controls */}
            <div className="flex items-center space-x-4">
              <Button
                onClick={() => adjustBet('decrease')}
                disabled={currentBetIndex === 0 || isSpinning}
                size="icon"
                className="glass-card border-casino-gold/30 hover:bg-casino-gold/20"
              >
                <Minus className="w-4 h-4 text-casino-gold" />
              </Button>

              <Card className="glass-card border-casino-gold/30 min-w-[120px]">
                <CardContent className="p-3 text-center">
                  <p className="text-xs text-gray-400 uppercase">Bet Amount</p>
                  <p className="text-xl font-bold text-casino-gold">
                    ${currentBet.toFixed(2)}
                  </p>
                </CardContent>
              </Card>

              <Button
                onClick={() => adjustBet('increase')}
                disabled={currentBetIndex === betAmounts.length - 1 || isSpinning}
                size="icon"
                className="glass-card border-casino-gold/30 hover:bg-casino-gold/20"
              >
                <Plus className="w-4 h-4 text-casino-gold" />
              </Button>

              <Button
                onClick={setMaxBet}
                disabled={isSpinning}
                className="bg-casino-red hover:bg-casino-red/80 text-white"
              >
                MAX BET
              </Button>
            </div>

            {/* Spin Button */}
            <Button
              onClick={performSpin}
              disabled={isSpinning || (!gameSession.freeSpinsRemaining && parseFloat(gameSession.currentBalance) < currentBet)}
              className="relative overflow-hidden bg-gradient-to-r from-casino-red via-red-600 to-casino-red hover:from-casino-red/80 hover:via-red-600/80 hover:to-casino-red/80 text-white font-bold px-12 py-6 text-xl"
            >
              <div className="flex items-center space-x-3">
                {isSpinning ? (
                  <RotateCcw className="w-6 h-6 animate-spin" />
                ) : (
                  <Play className="w-6 h-6" />
                )}
                <span>
                  {isSpinning ? "SPINNING..." :
                   gameSession.freeSpinsRemaining > 0 ? "FREE SPIN" : "SPIN"}
                </span>
              </div>
            </Button>

            {/* Auto Spin Controls */}
            <div className="flex items-center space-x-2">
              {!autoSpin ? (
                <>
                  <Button
                    onClick={() => startAutoSpin(10)}
                    disabled={isSpinning}
                    variant="outline"
                    size="sm"
                    className="border-casino-purple text-casino-purple hover:bg-casino-purple/20"
                  >
                    AUTO 10
                  </Button>
                  <Button
                    onClick={() => startAutoSpin(25)}
                    disabled={isSpinning}
                    variant="outline"
                    size="sm"
                    className="border-casino-purple text-casino-purple hover:bg-casino-purple/20"
                  >
                    AUTO 25
                  </Button>
                </>
              ) : (
                <Button
                  onClick={stopAutoSpin}
                  className="bg-casino-purple hover:bg-casino-purple/80 text-white"
                >
                  <Zap className="w-4 h-4 mr-2" />
                  STOP AUTO ({autoSpinCount})
                </Button>
              )}
            </div>

            {/* Game Stats */}
            <Card className="glass-card border-casino-gold/30">
              <CardContent className="p-3 text-center">
                <p className="text-xs text-gray-400 uppercase">Balance</p>
                <p className="text-xl font-bold text-casino-gold">
                  ${parseFloat(gameSession.currentBalance).toFixed(2)}
                </p>
              </CardContent>
            </Card>
          </div>
        )}
      </Card>

      {/* Win Celebration Modal */}
      {showWinCelebration && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 animate-in fade-in duration-300">
          <Card className="glass-card border-casino-gold/50 p-12 text-center max-w-md mx-4">
            <div className="text-6xl mb-4">🎉</div>
            <div className="text-3xl font-bold text-casino-gold mb-6 uppercase tracking-wide animate-pulse">
              BIG WIN!
            </div>
            <div className="text-5xl font-black text-transparent bg-clip-text bg-gradient-to-r from-casino-gold via-casino-red to-casino-purple mb-6">
              ${winAmount}
            </div>
            <div className="text-lg text-gray-300">Congratulations!</div>
          </Card>
        </div>
      )}

      {/* Bonus Modal */}
      {showBonusModal && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 animate-in fade-in duration-300">
          <Card className="glass-card border-casino-purple/50 p-12 text-center max-w-lg mx-4">
            <div className="text-6xl mb-6">⭐</div>
            <div className="text-4xl font-black text-casino-purple mb-4 uppercase tracking-wide">
              FREE SPINS ACTIVATED!
            </div>
            <div className="text-2xl font-bold text-casino-gold mb-6">
              You won {gameSession.freeSpinsRemaining} Free Spins with {gameSession.currentMultiplier}x Multiplier!
            </div>
          </Card>
        </div>
      )}
    </div>
  );
}

